# Tushare Pro 股票数据接口文档

## 概述

Tushare Pro 股票数据是最传统最有历史的数据服务项目，为广大投资者，尤其是量化投资者提供了稳定、便捷的接口。Pro版在继承了旧版API便捷易用性的同时，加强了数据的广度和深度。

**本文档基于Tushare Pro官方网站左侧菜单"股票数据"的完整分类结构生成，确保与官方文档保持一致。**

### 数据更新说明
- **行情数据**：共22个接口，包括历史日线、实时K线、分钟数据等
- **基础数据**：共11个接口，提供股票基础信息
- **财务数据**：共10个接口，涵盖三大报表和业绩数据
- **参考数据**：共5个接口，包括沪深港通、融资融券等
- **特色数据**：共4个接口，包括券商预测、筹码分布等
- **两融及转融通**：共7个接口，包括融资融券、转融通等
- **资金流向数据**：共8个接口，包括个股、板块、大盘资金流向等
- **打板专题数据**：共22个接口，包括涨跌停、龙虎榜、概念板块等

**总计89个股票数据接口**

## 股票代码规范

在Tushare数据接口里，股票代码参数都叫`ts_code`，每种股票代码都有规范的后缀：

| 交易所名称 | 交易所代码 | 股票代码后缀 | 备注 |
|-----------|-----------|-------------|------|
| 上海证券交易所 | SSE | .SH | 600000.SH(股票) 000001.SH(0开头指数) |
| 深圳证券交易所 | SZSE | .SZ | 000001.SZ(股票) 399005.SZ(3开头指数) |
| 北京证券交易所 | BSE | .BJ | 9、8和4开头的股票 |
| 香港证券交易所 | HKEX | .HK | 00001.HK |

## 一、基础数据

### 1.1 股票列表 (stock_basic)

**接口名称：** stock_basic  
**描述：** 获取基础信息数据，包括股票代码、名称、上市日期、退市日期等  
**权限：** 2000积分起  
**URL：** https://tushare.pro/document/2?doc_id=25

#### 输入参数

| 名称 | 类型 | 必选 | 描述 |
|------|------|------|------|
| ts_code | str | N | TS股票代码 |
| name | str | N | 名称 |
| market | str | N | 市场类别（主板/创业板/科创板/CDR/北交所） |
| list_status | str | N | 上市状态 L上市 D退市 P暂停上市，默认是L |
| exchange | str | N | 交易所 SSE上交所 SZSE深交所 BSE北交所 |
| is_hs | str | N | 是否沪深港通标的，N否 H沪股通 S深股通 |

#### 输出参数

| 名称 | 类型 | 默认显示 | 描述 |
|------|------|----------|------|
| ts_code | str | Y | TS代码 |
| symbol | str | Y | 股票代码 |
| name | str | Y | 股票名称 |
| area | str | Y | 地域 |
| industry | str | Y | 所属行业 |
| fullname | str | N | 股票全称 |
| enname | str | N | 英文全称 |
| cnspell | str | Y | 拼音缩写 |
| market | str | Y | 市场类型（主板/创业板/科创板/CDR） |
| exchange | str | N | 交易所代码 |
| curr_type | str | N | 交易货币 |
| list_status | str | N | 上市状态 L上市 D退市 P暂停上市 |
| list_date | str | Y | 上市日期 |
| delist_date | str | N | 退市日期 |
| is_hs | str | N | 是否沪深港通标的，N否 H沪股通 S深股通 |
| act_name | str | Y | 实控人名称 |
| act_ent_type | str | Y | 实控人企业性质 |

#### 接口示例

```python
pro = ts.pro_api()
# 查询当前所有正常上市交易的股票列表
data = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date')
```

### 1.2 基础数据完整接口列表（共11个接口）

| 接口名称 | 描述 | doc_id | URL |
|----------|------|--------|-----|
| 股票列表 | 获取基础信息数据，包括股票代码、名称、上市日期、退市日期等 | 25 | https://tushare.pro/document/2?doc_id=25 |
| 每日股本（盘前） | 获取每日股本数据 | 329 | https://tushare.pro/document/2?doc_id=329 |
| 交易日历 | 获取各交易所交易日历 | 26 | https://tushare.pro/document/2?doc_id=26 |
| 股票曾用名 | 获取股票曾用名信息 | 100 | https://tushare.pro/document/2?doc_id=100 |
| 沪深股通成分股 | 获取沪深股通成分股数据 | 104 | https://tushare.pro/document/2?doc_id=104 |
| 上市公司基本信息 | 获取上市公司基本信息 | 112 | https://tushare.pro/document/2?doc_id=112 |
| 上市公司管理层 | 获取上市公司管理层信息 | 193 | https://tushare.pro/document/2?doc_id=193 |
| 管理层薪酬和持股 | 获取管理层薪酬和持股数据 | 194 | https://tushare.pro/document/2?doc_id=194 |
| 北交所新旧代码对照 | 获取北交所新旧代码对照表 | 375 | https://tushare.pro/document/2?doc_id=375 |
| IPO新股上市 | 获取IPO新股上市信息 | 123 | https://tushare.pro/document/2?doc_id=123 |
| 股票历史列表 | 获取股票历史列表 | 262 | https://tushare.pro/document/2?doc_id=262 |

## 二、行情数据

行情数据提供了包括股票、指数、基金、期货等在内的高质量日线行情和分钟行情数据。

### 2.1 行情数据完整接口列表（共22个接口）

| 接口名称 | 描述 | doc_id | URL |
|----------|------|--------|-----|
| 历史日线 | 获取A股日线行情数据 | 27 | https://tushare.pro/document/2?doc_id=27 |
| 实时K线 | 获取实时K线数据 | 372 | https://tushare.pro/document/2?doc_id=372 |
| 历史分钟 | 获取历史分钟级行情数据 | 370 | https://tushare.pro/document/2?doc_id=370 |
| 实时分钟 | 获取实时分钟级行情数据 | 374 | https://tushare.pro/document/2?doc_id=374 |
| 周线行情 | 获取周线行情数据 | 144 | https://tushare.pro/document/2?doc_id=144 |
| 月线行情 | 获取月线行情数据 | 145 | https://tushare.pro/document/2?doc_id=145 |
| 复权行情 | 获取复权行情数据 | 146 | https://tushare.pro/document/2?doc_id=146 |
| 周/月线行情(每日更新) | 获取每日更新的周月线行情 | 336 | https://tushare.pro/document/2?doc_id=336 |
| 周/月线复权行情(每日更新) | 获取每日更新的周月线复权行情 | 365 | https://tushare.pro/document/2?doc_id=365 |
| 复权因子 | 获取复权因子数据 | 28 | https://tushare.pro/document/2?doc_id=28 |
| 实时Tick（爬虫） | 获取实时Tick数据 | 315 | https://tushare.pro/document/2?doc_id=315 |
| 实时成交（爬虫） | 获取实时成交数据 | 316 | https://tushare.pro/document/2?doc_id=316 |
| 实时排名（爬虫） | 获取实时排名数据 | 317 | https://tushare.pro/document/2?doc_id=317 |
| 每日指标 | 获取每日行情指标 | 32 | https://tushare.pro/document/2?doc_id=32 |
| 通用行情接口 | 通用行情数据接口 | 109 | https://tushare.pro/document/2?doc_id=109 |
| 每日涨跌停价格 | 获取每日涨跌停价格 | 183 | https://tushare.pro/document/2?doc_id=183 |
| 每日停复牌信息 | 获取每日停复牌信息 | 214 | https://tushare.pro/document/2?doc_id=214 |
| 沪深股通十大成交股 | 获取沪深股通十大成交股 | 48 | https://tushare.pro/document/2?doc_id=48 |
| 港股通十大成交股 | 获取港股通十大成交股 | 49 | https://tushare.pro/document/2?doc_id=49 |
| 港股通每日成交统计 | 获取港股通每日成交统计 | 196 | https://tushare.pro/document/2?doc_id=196 |
| 港股通每月成交统计 | 获取港股通每月成交统计 | 197 | https://tushare.pro/document/2?doc_id=197 |
| 备用行情 | 获取备用行情数据 | 255 | https://tushare.pro/document/2?doc_id=255 |

## 三、财务数据

Pro版的财务数据提供完整的财务指标和全部历史数据，同时也提供高质量的业绩预告和业绩快报数据。

### 3.1 财务数据完整接口列表（共10个接口）

| 接口名称 | 描述 | doc_id | URL |
|----------|------|--------|-----|
| 利润表 | 获取上市公司利润表数据 | 33 | https://tushare.pro/document/2?doc_id=33 |
| 资产负债表 | 获取上市公司资产负债表数据 | 36 | https://tushare.pro/document/2?doc_id=36 |
| 现金流量表 | 获取上市公司现金流量表数据 | 44 | https://tushare.pro/document/2?doc_id=44 |
| 业绩预告 | 获取上市公司业绩预告数据 | 45 | https://tushare.pro/document/2?doc_id=45 |
| 业绩快报 | 获取上市公司业绩快报数据 | 46 | https://tushare.pro/document/2?doc_id=46 |
| 分红送股数据 | 获取分红送股数据 | 103 | https://tushare.pro/document/2?doc_id=103 |
| 财务指标数据 | 获取上市公司财务指标数据 | 79 | https://tushare.pro/document/2?doc_id=79 |
| 财务审计意见 | 获取财务审计意见数据 | 80 | https://tushare.pro/document/2?doc_id=80 |
| 主营业务构成 | 获取主营业务构成数据 | 81 | https://tushare.pro/document/2?doc_id=81 |
| 财报披露日期表 | 获取财报披露日期表数据 | 162 | https://tushare.pro/document/2?doc_id=162 |

### 3.2 财务数据说明

Pro版的财务数据跟旧版有着明显的差异，Pro提供的是完整的财务指标和全部历史数据，同时也提供质量比较高的业绩预告和业绩快报数据。

## 四、参考数据

参考数据提供市场行为和公司治理方面的参考数据，这一部分数据可以为投资者提供具有更多发掘价值的信息。

**URL：** https://tushare.pro/document/2?doc_id=17

### 4.1 参考数据完整接口列表（共5个接口）

| 接口名称 | 描述 | doc_id | URL |
|----------|------|--------|-----|
| 沪深港通资金流向 | 获取沪深港通资金流向数据 | 47 | https://tushare.pro/document/2?doc_id=47 |
| 沪深股通十大成交股 | 获取沪深股通十大成交股数据 | 48 | https://tushare.pro/document/2?doc_id=48 |
| 港股通十大成交股 | 获取港股通十大成交股数据 | 49 | https://tushare.pro/document/2?doc_id=49 |
| 融资融券交易汇总 | 获取融资融券交易汇总数据 | 58 | https://tushare.pro/document/2?doc_id=58 |
| 融资融券交易明细 | 获取融资融券交易明细数据 | 59 | https://tushare.pro/document/2?doc_id=59 |

## 五、特色数据

Tushare股票特色数据提供独有的数据服务，包括卖方盈利预测、股票每日筹码和成本数据等。

**URL：** https://tushare.pro/document/2?doc_id=291

### 5.1 特色数据完整接口列表（共4个接口）

| 接口名称 | 描述 | doc_id | URL |
|----------|------|--------|-----|
| 券商（卖方）盈利预测数据 | 来自券商研究报告的盈利预测和评级数据，从2010年开始，每天20点更新 | 292 | https://tushare.pro/document/2?doc_id=292 |
| 股票每日筹码成本和胜率 | 每个股票每天的平均成本以及胜率数据，包括历史最高价、最低价、各分位持仓成本价格等 | 293 | https://tushare.pro/document/2?doc_id=293 |
| 股票每日筹码分布 | 每个股票每天的持仓价位及占比，详细展现投资者持仓成本情况 | 294 | https://tushare.pro/document/2?doc_id=294 |
| 券商金股数据 | 券商研究所每月发布的十大金股数据，代表对市场热点的观点 | 267 | https://tushare.pro/document/2?doc_id=267 |

## 六、两融及转融通

提供融资融券及转融通相关数据，包括融资融券余额、明细等数据。

**URL：** https://tushare.pro/document/2?doc_id=330

### 6.1 两融及转融通完整接口列表（共7个接口）

| 接口名称 | 描述 | doc_id | URL |
|----------|------|--------|-----|
| 融资融券交易汇总 | 获取融资融券每日交易汇总数据 | 58 | https://tushare.pro/document/2?doc_id=58 |
| 融资融券交易明细 | 获取融资融券交易明细数据 | 59 | https://tushare.pro/document/2?doc_id=59 |
| 融资融券标的（盘前） | 获取融资融券标的股票数据 | 326 | https://tushare.pro/document/2?doc_id=326 |
| 转融券交易汇总(停） | 获取转融券交易汇总数据（已停用） | 332 | https://tushare.pro/document/2?doc_id=332 |
| 转融资交易汇总 | 获取转融资交易汇总数据 | 331 | https://tushare.pro/document/2?doc_id=331 |
| 转融券交易明细(停） | 获取转融券交易明细数据（已停用） | 333 | https://tushare.pro/document/2?doc_id=333 |
| 做市借券交易汇总(停） | 获取做市借券交易汇总数据（已停用） | 334 | https://tushare.pro/document/2?doc_id=334 |

## 七、资金流向数据

提供资金流向相关数据分析，包括主力资金流向、北向资金等数据。

**URL：** https://tushare.pro/document/2?doc_id=342

### 7.1 资金流向数据完整接口列表（共8个接口）

| 接口名称 | 描述 | doc_id | URL |
|----------|------|--------|-----|
| 个股资金流向 | 获取个股资金流向数据 | 170 | https://tushare.pro/document/2?doc_id=170 |
| 个股资金流向（THS） | 获取同花顺个股资金流向数据 | 348 | https://tushare.pro/document/2?doc_id=348 |
| 个股资金流向（DC） | 获取东方财富个股资金流向数据 | 349 | https://tushare.pro/document/2?doc_id=349 |
| 板块资金流向（THS) | 获取同花顺板块资金流向数据 | 371 | https://tushare.pro/document/2?doc_id=371 |
| 行业资金流向（THS） | 获取同花顺行业资金流向数据 | 343 | https://tushare.pro/document/2?doc_id=343 |
| 板块资金流向（DC） | 获取东方财富板块资金流向数据 | 344 | https://tushare.pro/document/2?doc_id=344 |
| 大盘资金流向（DC） | 获取东方财富大盘资金流向数据 | 345 | https://tushare.pro/document/2?doc_id=345 |
| 沪深港通资金流向 | 获取沪深港通资金流向数据 | 47 | https://tushare.pro/document/2?doc_id=47 |

## 八、打板专题数据

提供打板相关的专题数据，包括涨停板、跌停板等数据分析。

**URL：** https://tushare.pro/document/2?doc_id=346

### 8.1 打板专题数据完整接口列表（共22个接口）

| 接口名称 | 描述 | doc_id | URL |
|----------|------|--------|-----|
| 题材数据（开盘啦） | 获取开盘啦题材数据 | 350 | https://tushare.pro/document/2?doc_id=350 |
| 题材成分（开盘啦） | 获取开盘啦题材成分数据 | 351 | https://tushare.pro/document/2?doc_id=351 |
| 榜单数据（开盘啦） | 获取开盘啦涨停、跌停、炸板等榜单数据 | 347 | https://tushare.pro/document/2?doc_id=347 |
| 龙虎榜每日统计单 | 获取龙虎榜每日统计数据 | 106 | https://tushare.pro/document/2?doc_id=106 |
| 龙虎榜机构交易单 | 获取龙虎榜机构交易数据 | 107 | https://tushare.pro/document/2?doc_id=107 |
| 同花顺涨跌停榜单 | 获取同花顺涨跌停榜单数据 | 355 | https://tushare.pro/document/2?doc_id=355 |
| 涨跌停和炸板数据 | 获取涨跌停和炸板数据 | 298 | https://tushare.pro/document/2?doc_id=298 |
| 涨停股票连板天梯 | 获取涨停股票连板天梯数据 | 356 | https://tushare.pro/document/2?doc_id=356 |
| 涨停最强板块统计 | 获取涨停最强板块统计数据 | 357 | https://tushare.pro/document/2?doc_id=357 |
| 同花顺行业概念板块 | 获取同花顺行业概念板块数据 | 259 | https://tushare.pro/document/2?doc_id=259 |
| 同花顺概念和行业指数行情 | 获取同花顺概念和行业指数行情数据 | 260 | https://tushare.pro/document/2?doc_id=260 |
| 同花顺行业概念成分 | 获取同花顺行业概念成分数据 | 261 | https://tushare.pro/document/2?doc_id=261 |
| 东方财富概念板块 | 获取东方财富概念板块数据 | 362 | https://tushare.pro/document/2?doc_id=362 |
| 东方财富概念成分 | 获取东方财富概念成分数据 | 363 | https://tushare.pro/document/2?doc_id=363 |
| 东财概念和行业指数行情 | 获取东财概念和行业指数行情数据 | 382 | https://tushare.pro/document/2?doc_id=382 |
| 开盘竞价成交（当日） | 获取开盘竞价成交数据 | 369 | https://tushare.pro/document/2?doc_id=369 |
| 市场游资最全名录 | 获取市场游资最全名录数据 | 311 | https://tushare.pro/document/2?doc_id=311 |
| 游资交易每日明细 | 获取游资交易每日明细数据 | 312 | https://tushare.pro/document/2?doc_id=312 |
| 同花顺App热榜数 | 获取同花顺App热榜数据 | 320 | https://tushare.pro/document/2?doc_id=320 |
| 东方财富App热榜 | 获取东方财富App热榜数据 | 321 | https://tushare.pro/document/2?doc_id=321 |
| 通达信板块信息 | 获取通达信板块信息数据 | 376 | https://tushare.pro/document/2?doc_id=376 |
| 通达信板块成分 | 获取通达信板块成分数据 | 377 | https://tushare.pro/document/2?doc_id=377 |
| 通达信板块行情 | 获取通达信板块行情数据 | 378 | https://tushare.pro/document/2?doc_id=378 |

## 九、完整接口索引

### 9.1 按分类快速索引

| 分类 | 主要接口 | 数量 |
|------|----------|------|
| 基础数据 | 股票列表、交易日历、上市公司信息等 | 11个 |
| 行情数据 | 日线、分钟线、复权数据等 | 22个 |
| 财务数据 | 三大报表、业绩预告、财务指标等 | 10个 |
| 参考数据 | 沪深港通、融资融券等 | 5个 |
| 特色数据 | 券商预测、筹码分布等 | 4个 |
| 两融及转融通 | 融资融券相关数据 | 7个 |
| 资金流向 | 主力资金、北向资金等 | 8个 |
| 打板专题 | 涨跌停板相关数据 | 22个 |

**股票数据总接口数量：89个**

### 9.2 高频使用接口推荐

| 接口名称 | 用途 | 推荐指数 |
|----------|------|----------|
| stock_basic | 获取股票基础信息 | ⭐⭐⭐⭐⭐ |
| daily | 获取日线行情数据 | ⭐⭐⭐⭐⭐ |
| trade_cal | 获取交易日历 | ⭐⭐⭐⭐⭐ |
| income | 获取利润表数据 | ⭐⭐⭐⭐ |
| balancesheet | 获取资产负债表 | ⭐⭐⭐⭐ |
| cashflow | 获取现金流量表 | ⭐⭐⭐⭐ |
| daily_basic | 获取每日指标 | ⭐⭐⭐⭐ |

## 使用说明

1. 所有接口都需要Tushare Pro的token进行认证
2. 不同接口有不同的积分要求，请查看具体接口文档
3. 建议将基础数据保存到本地存储后使用
4. 可以通过[数据工具](https://tushare.pro/webclient/)调试和查看数据
5. 建议按照以下顺序使用接口：
   - 首先获取股票基础信息（stock_basic）
   - 然后获取交易日历（trade_cal）
   - 再获取行情数据（daily等）
   - 最后获取财务和其他数据

## 联系方式

- QQ群: 958517905
- 公众号：waditu
- Github：https://github.com/waditu
- 官网：https://tushare.pro

---

*本文档基于Tushare Pro官方文档整理，详细参数和使用方法请参考官方文档。*
